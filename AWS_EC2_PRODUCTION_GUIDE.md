# 🚀 AWS EC2 Production Deployment Guide - XAUUSD AI Trading System

## 📋 Complete Production Setup on AWS Ubuntu EC2

### 🎯 **Overview**
This guide will help you deploy your XAUUSD AI Trading System on AWS EC2 Ubuntu for production use with high availability, security, and performance.

---

## 🔧 **Step 1: AWS EC2 Instance Setup**

### **1.1 Launch EC2 Instance**

```bash
# Recommended Instance Configuration:
Instance Type: t3.large (2 vCPU, 8 GB RAM) or c5.xlarge (4 vCPU, 8 GB RAM)
AMI: Ubuntu Server 22.04 LTS (ami-0c02fb55956c7d316)
Storage: 50 GB GP3 SSD
Security Group: Custom (see below)
Key Pair: Create new or use existing
```

### **1.2 Security Group Configuration**

```bash
# Inbound Rules:
Port 22 (SSH): Your IP only
Port 80 (HTTP): 0.0.0.0/0
Port 443 (HTTPS): 0.0.0.0/0
Port 8000 (API): Your IP or specific IPs only
Port 3000 (Monitoring): Your IP only

# Outbound Rules:
All traffic: 0.0.0.0/0 (for API calls and updates)
```

### **1.3 Elastic IP (Recommended)**

```bash
# Allocate and associate Elastic IP for consistent access
aws ec2 allocate-address --domain vpc
aws ec2 associate-address --instance-id i-1234567890abcdef0 --allocation-id eipalloc-12345678
```

---

## 🔐 **Step 2: Initial Server Setup**

### **2.1 Connect to Instance**

```bash
# Connect via SSH
ssh -i your-key.pem ubuntu@your-elastic-ip

# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git htop nano vim unzip software-properties-common
```

### **2.2 Create Application User**

```bash
# Create dedicated user for the application
sudo adduser xauusd-ai
sudo usermod -aG sudo xauusd-ai

# Switch to application user
sudo su - xauusd-ai
```

### **2.3 Setup Firewall**

```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 8000
sudo ufw status
```

---

## 🐍 **Step 3: Python Environment Setup**

### **3.1 Install Python 3.11**

```bash
# Add Python PPA
sudo add-apt-repository ppa:deadsnakes/ppa -y
sudo apt update

# Install Python 3.11
sudo apt install -y python3.11 python3.11-venv python3.11-dev python3-pip

# Set Python 3.11 as default
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
```

### **3.2 Install System Dependencies**

```bash
# Install required system packages
sudo apt install -y build-essential libssl-dev libffi-dev python3-dev
sudo apt install -y postgresql postgresql-contrib redis-server nginx
sudo apt install -y supervisor htop iotop nethogs
```

---

## 📦 **Step 4: Application Deployment**

### **4.1 Clone Repository**

```bash
# Create application directory
mkdir -p /home/<USER>/xauusd-ai-system
cd /home/<USER>/xauusd-ai-system

# Clone your repository (replace with your repo URL)
git clone https://github.com/your-username/xauusd-ai-system.git .

# Or upload files via SCP
# scp -i your-key.pem -r ./TRVBOT ubuntu@your-ip:/home/<USER>/xauusd-ai-system/
```

### **4.2 Setup Python Virtual Environment**

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip setuptools wheel
```

### **4.3 Install Python Dependencies**

```bash
# Install PyTorch with CUDA support (if GPU instance)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install other dependencies
pip install -r requirements.txt

# Additional production packages
pip install gunicorn uvicorn[standard] supervisor psutil
```

---

## 🗄️ **Step 5: Database Setup**

### **5.1 PostgreSQL Configuration**

```bash
# Start PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql
```

```sql
-- In PostgreSQL shell
CREATE DATABASE xauusd_ai;
CREATE USER xauusd_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE xauusd_ai TO xauusd_user;
\q
```

### **5.2 Redis Configuration**

```bash
# Configure Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis
redis-cli ping
```

---

## ⚙️ **Step 6: Environment Configuration**

### **6.1 Production Environment File**

```bash
# Create production .env file
cat > .env << EOF
# Production Environment Configuration
ENVIRONMENT=production
DEBUG=False

# Database Configuration
DATABASE_URL=postgresql://xauusd_user:your_secure_password@localhost/xauusd_ai
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# AI Model Configuration
MODEL_TYPE=enhanced_lstm
SEQUENCE_LENGTH=120
CONFIDENCE_THRESHOLD=0.7
SIGNAL_THRESHOLD=0.6

# Data Source Configuration
SYMBOL=GC=F
DATA_SOURCE=yfinance
UPDATE_INTERVAL=900  # 15 minutes

# Security
SECRET_KEY=your_very_secure_secret_key_here
JWT_SECRET=your_jwt_secret_key_here

# Logging
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/xauusd-ai-system/logs/production.log

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=3000

# TradingView Integration
TRADINGVIEW_WEBHOOK_URL=https://your-domain.com/webhook/tradingview
WEBHOOK_SECRET=your_webhook_secret

# Email Notifications (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# AWS Configuration (for S3 model storage)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET=xauusd-ai-models
EOF
```

### **6.2 Create Required Directories**

```bash
# Create necessary directories
mkdir -p logs models data backups
chmod 755 logs models data backups
```

---

## 🔄 **Step 7: Process Management with Supervisor**

### **7.1 Supervisor Configuration**

```bash
# Create supervisor config
sudo tee /etc/supervisor/conf.d/xauusd-ai.conf << EOF
[program:xauusd-ai-api]
command=/home/<USER>/xauusd-ai-system/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 api_server:app
directory=/home/<USER>/xauusd-ai-system
user=xauusd-ai
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/xauusd-ai-system/logs/api.log
environment=PATH="/home/<USER>/xauusd-ai-system/venv/bin"

[program:xauusd-ai-trainer]
command=/home/<USER>/xauusd-ai-system/venv/bin/python train_model_5year.py
directory=/home/<USER>/xauusd-ai-system
user=xauusd-ai
autostart=false
autorestart=false
redirect_stderr=true
stdout_logfile=/home/<USER>/xauusd-ai-system/logs/training.log
environment=PATH="/home/<USER>/xauusd-ai-system/venv/bin"

[program:xauusd-ai-data-collector]
command=/home/<USER>/xauusd-ai-system/venv/bin/python data_collector.py --continuous
directory=/home/<USER>/xauusd-ai-system
user=xauusd-ai
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/xauusd-ai-system/logs/data_collector.log
environment=PATH="/home/<USER>/xauusd-ai-system/venv/bin"
EOF
```

### **7.2 Start Services**

```bash
# Reload supervisor configuration
sudo supervisorctl reread
sudo supervisorctl update

# Start services
sudo supervisorctl start xauusd-ai-api
sudo supervisorctl start xauusd-ai-data-collector

# Check status
sudo supervisorctl status
```

---

## 🌐 **Step 8: Nginx Reverse Proxy**

### **8.1 Nginx Configuration**

```bash
# Create Nginx configuration
sudo tee /etc/nginx/sites-available/xauusd-ai << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration (Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # API proxy
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Webhook endpoint
    location /webhook/ {
        proxy_pass http://127.0.0.1:8000/webhook/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Health check
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
    
    # Static files (if any)
    location /static/ {
        alias /home/<USER>/xauusd-ai-system/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/xauusd-ai /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### **8.2 SSL Certificate with Let's Encrypt**

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 📊 **Step 9: Monitoring and Logging**

### **9.1 System Monitoring Script**

```bash
# Create monitoring script
cat > monitor_system.py << EOF
#!/usr/bin/env python3
import psutil
import requests
import time
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_system_health():
    # CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # Memory usage
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
    
    # Disk usage
    disk = psutil.disk_usage('/')
    disk_percent = disk.percent
    
    # API health check
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        api_status = response.status_code == 200
    except:
        api_status = False
    
    # Log metrics
    logger.info(f"CPU: {cpu_percent}%, Memory: {memory_percent}%, Disk: {disk_percent}%, API: {api_status}")
    
    # Alerts
    if cpu_percent > 80:
        logger.warning(f"High CPU usage: {cpu_percent}%")
    if memory_percent > 80:
        logger.warning(f"High memory usage: {memory_percent}%")
    if disk_percent > 80:
        logger.warning(f"High disk usage: {disk_percent}%")
    if not api_status:
        logger.error("API health check failed!")

if __name__ == "__main__":
    while True:
        check_system_health()
        time.sleep(300)  # Check every 5 minutes
EOF

chmod +x monitor_system.py
```

### **9.2 Log Rotation**

```bash
# Configure logrotate
sudo tee /etc/logrotate.d/xauusd-ai << EOF
/home/<USER>/xauusd-ai-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 xauusd-ai xauusd-ai
    postrotate
        supervisorctl restart xauusd-ai-api
    endscript
}
EOF
```

---

## 🔄 **Step 10: Automated Backups**

### **10.1 Backup Script**

```bash
# Create backup script
cat > backup_system.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="xauusd_ai_backup_$DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup models
cp -r models/ "$BACKUP_DIR/$BACKUP_NAME/"

# Backup configuration
cp .env "$BACKUP_DIR/$BACKUP_NAME/"

# Backup database
pg_dump -h localhost -U xauusd_user xauusd_ai > "$BACKUP_DIR/$BACKUP_NAME/database.sql"

# Backup logs (last 7 days)
find logs/ -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/$BACKUP_NAME/" \;

# Compress backup
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

# Keep only last 30 backups
ls -t *.tar.gz | tail -n +31 | xargs -r rm

echo "Backup completed: $BACKUP_NAME.tar.gz"
EOF

chmod +x backup_system.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/xauusd-ai-system/backup_system.sh
```

---

## 🚀 **Step 11: Production Deployment**

### **11.1 Train Enhanced Model**

```bash
# Activate virtual environment
source venv/bin/activate

# Train the enhanced 5-year model
python train_model_5year.py

# This will take several hours - monitor progress
tail -f logs/training_5year.log
```

### **11.2 Start All Services**

```bash
# Start all services
sudo supervisorctl start all

# Check status
sudo supervisorctl status

# Test API
curl http://localhost:8000/health
curl https://your-domain.com/api/health
```

### **11.3 Performance Testing**

```bash
# Install testing tools
pip install locust

# Create load test
cat > load_test.py << EOF
from locust import HttpUser, task, between

class APIUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def health_check(self):
        self.client.get("/health")
    
    @task
    def get_status(self):
        self.client.get("/status")
    
    @task
    def generate_signal(self):
        self.client.post("/signal", json={"timeframe": "1h"})
EOF

# Run load test
locust -f load_test.py --host=http://localhost:8000
```

---

## 📈 **Step 12: Monitoring Dashboard**

### **12.1 Simple Monitoring Dashboard**

```bash
# Create simple dashboard
cat > dashboard.py << EOF
from flask import Flask, render_template_string
import psutil
import requests
import json

app = Flask(__name__)

@app.route('/')
def dashboard():
    # System metrics
    cpu = psutil.cpu_percent()
    memory = psutil.virtual_memory().percent
    disk = psutil.disk_usage('/').percent
    
    # API status
    try:
        api_response = requests.get('http://localhost:8000/status', timeout=5)
        api_data = api_response.json() if api_response.status_code == 200 else {}
    except:
        api_data = {}
    
    template = '''
    <!DOCTYPE html>
    <html>
    <head><title>XAUUSD AI System Dashboard</title></head>
    <body>
        <h1>XAUUSD AI System Dashboard</h1>
        <h2>System Metrics</h2>
        <p>CPU Usage: {{ cpu }}%</p>
        <p>Memory Usage: {{ memory }}%</p>
        <p>Disk Usage: {{ disk }}%</p>
        
        <h2>AI System Status</h2>
        <pre>{{ api_data }}</pre>
        
        <script>
            setTimeout(function(){ location.reload(); }, 30000);
        </script>
    </body>
    </html>
    '''
    
    return render_template_string(template, 
                                cpu=cpu, 
                                memory=memory, 
                                disk=disk, 
                                api_data=json.dumps(api_data, indent=2))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=False)
EOF

# Install Flask
pip install flask

# Add dashboard to supervisor
sudo tee -a /etc/supervisor/conf.d/xauusd-ai.conf << EOF

[program:xauusd-ai-dashboard]
command=/home/<USER>/xauusd-ai-system/venv/bin/python dashboard.py
directory=/home/<USER>/xauusd-ai-system
user=xauusd-ai
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/xauusd-ai-system/logs/dashboard.log
environment=PATH="/home/<USER>/xauusd-ai-system/venv/bin"
EOF

sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start xauusd-ai-dashboard
```

---

## 🔐 **Step 13: Security Hardening**

### **13.1 Additional Security Measures**

```bash
# Install fail2ban
sudo apt install -y fail2ban

# Configure fail2ban for SSH
sudo tee /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
EOF

sudo systemctl restart fail2ban

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# Update system packages regularly
echo '0 4 * * * apt update && apt upgrade -y' | sudo crontab -
```

---

## ✅ **Step 14: Final Verification**

### **14.1 System Health Check**

```bash
# Check all services
sudo supervisorctl status
sudo systemctl status nginx
sudo systemctl status postgresql
sudo systemctl status redis-server

# Test API endpoints
curl https://your-domain.com/api/health
curl https://your-domain.com/api/status
curl -X POST https://your-domain.com/api/signal -H "Content-Type: application/json" -d '{"timeframe":"1h"}'

# Check logs
tail -f logs/production.log
tail -f logs/api.log
```

### **14.2 Performance Verification**

```bash
# Check system resources
htop
iotop
nethogs

# Monitor API performance
curl -w "@curl-format.txt" -o /dev/null -s https://your-domain.com/api/health
```

---

## 🎯 **Production Checklist**

### **✅ Pre-Launch Checklist**

- [ ] EC2 instance configured with appropriate size
- [ ] Security groups properly configured
- [ ] Elastic IP assigned
- [ ] SSL certificate installed and working
- [ ] Database setup and secured
- [ ] All services running via Supervisor
- [ ] Nginx reverse proxy configured
- [ ] Monitoring and logging in place
- [ ] Backup system configured
- [ ] Security hardening applied
- [ ] Load testing completed
- [ ] API endpoints tested
- [ ] Enhanced 5-year model trained
- [ ] Dashboard accessible
- [ ] Alerts configured

### **🚀 Go-Live Steps**

1. **Final Model Training**: Run `python train_model_5year.py`
2. **Service Startup**: `sudo supervisorctl start all`
3. **Health Verification**: Test all API endpoints
4. **TradingView Integration**: Update webhook URLs
5. **Monitoring Setup**: Verify dashboard and alerts
6. **Documentation**: Update production URLs and credentials

---

**Your XAUUSD AI Trading System is now ready for production on AWS EC2!** 🎉

**Access Points:**
- **API**: https://your-domain.com/api/
- **Dashboard**: https://your-domain.com:3000/
- **Health Check**: https://your-domain.com/api/health
